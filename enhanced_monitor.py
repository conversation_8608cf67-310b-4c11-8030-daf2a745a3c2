#!/usr/bin/env python3
"""
مراقب Pastebin المحسن - للأغراض البحثية والتعليمية
نسخة محسنة تستخدم ملف الإعدادات القابل للتخصيص
"""

import requests
import time
import random
from datetime import datetime
from config import (
    get_enabled_patterns, 
    get_pattern_descriptions,
    DEFAULT_CHECK_INTERVAL,
    API_SETTINGS,
    DISPLAY_SETTINGS,
    SECURITY_SETTINGS,
    MAX_MATCHES_DISPLAY
)

class EnhancedPastebinMonitor:
    def __init__(self, api_key=None, demo_mode=True):
        """
        تهيئة مراقب Pastebin المحسن
        
        Args:
            api_key: مفتاح API الخاص بـ Pastebin (اختياري)
            demo_mode: وضع التجريب (افتراضي: True)
        """
        self.api_key = api_key
        self.demo_mode = demo_mode
        self.base_url = API_SETTINGS['base_url']
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': API_SETTINGS['user_agent']
        })
        
        # الحصول على الأنماط المفعلة من ملف الإعدادات
        self.patterns = get_enabled_patterns()
        self.pattern_descriptions = get_pattern_descriptions()
        
        # مجموعة لتتبع اللصق المعالج
        self.processed_pastes = set()
        
        # إحصائيات
        self.stats = {
            'total_pastes_checked': 0,
            'total_matches_found': 0,
            'patterns_found': {name: 0 for name in self.patterns.keys()},
            'start_time': datetime.now()
        }
    
    def get_sample_pastes(self):
        """
        إنشاء بيانات تجريبية للاختبار
        """
        sample_pastes = []
        
        # إنشاء معرفات عشوائية للصق
        for i in range(random.randint(1, 4)):
            paste_id = f"demo_{random.randint(1000, 9999)}"
            sample_pastes.append({
                'id': paste_id,
                'url': f"{self.base_url}/{paste_id}",
                'title': f'Demo Paste {i+1}',
                'date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'size': random.randint(500, 5000)
            })
        
        return sample_pastes
    
    def get_sample_content(self, paste_id):
        """
        إنشاء محتوى تجريبي متنوع
        """
        sample_contents = [
            # محتوى يحتوي على عناوين العملات المشفرة
            """
Configuration backup file:
bitcoin_main_wallet=**********************************
ethereum_contract=******************************************
backup_date=2024-01-15
admin_email=<EMAIL>
server_ip=*************
            """,
            
            # محتوى يحتوي على عناوين متعددة
            """
Wallet addresses for project:
BTC addresses:
- **********************************
- **********************************

ETH addresses:
- 0x8ba1f109551bD432803012645Hac136c22C501e
- ******************************************

Contact: <EMAIL>
Phone: ******-123-4567
            """,
            
            # محتوى عادي بدون أنماط
            """
Regular text file with no sensitive patterns.
Just some normal content here for testing.
Random numbers: 12345, 67890
Some text and more text.
Nothing interesting to find in this paste.
            """,
            
            # محتوى يحتوي على أنماط مختلطة
            """
System configuration log:
[2024-01-15 10:30:15] User login: <EMAIL>
[2024-01-15 10:31:22] BTC transaction: **********************************
[2024-01-15 10:31:25] ETH contract: ******************************************
[2024-01-15 10:32:10] Server IP: ********
[2024-01-15 10:32:15] API key: abc123def456ghi789jkl012mno345pqr678
            """,
            
            # محتوى يحتوي على مفاتيح حساسة (للاختبار فقط)
            """
Development environment setup:
database_host=db.example.com
database_user=admin
database_pass=secret123
api_endpoint=https://api.service.com
btc_cold_storage=**********************************
eth_multisig=******************************************
support_email=<EMAIL>
            """
        ]
        
        # اختيار محتوى عشوائي
        return random.choice(sample_contents)
    
    def search_patterns(self, content):
        """
        البحث عن الأنماط في المحتوى
        """
        found_patterns = {}
        
        for pattern_name, pattern_regex in self.patterns.items():
            matches = pattern_regex.findall(content)
            if matches:
                # إزالة التكرارات
                unique_matches = list(set(matches))
                found_patterns[pattern_name] = unique_matches
                
                # تحديث الإحصائيات
                self.stats['patterns_found'][pattern_name] += len(unique_matches)
        
        if found_patterns:
            self.stats['total_matches_found'] += 1
        
        return found_patterns
    
    def display_match(self, paste_info, found_patterns):
        """
        عرض التطابق بتنسيق محسن
        """
        print("\n" + "="*60)
        print(f"🎯 تم العثور على تطابق في: {paste_info['url']}")
        
        if DISPLAY_SETTINGS['show_timestamps']:
            print(f"📅 التاريخ: {paste_info['date']}")
        
        title = paste_info['title']
        if len(title) > DISPLAY_SETTINGS['max_title_length']:
            title = title[:DISPLAY_SETTINGS['max_title_length']] + "..."
        print(f"📝 العنوان: {title}")
        
        if DISPLAY_SETTINGS['show_paste_size']:
            print(f"📏 الحجم: {paste_info.get('size', 'غير محدد')} بايت")
        
        for pattern_name, matches in found_patterns.items():
            description = self.pattern_descriptions.get(pattern_name, pattern_name)
            print(f"\n🔍 النمط: {pattern_name} ({description})")
            print(f"📊 عدد التطابقات: {len(matches)}")
            
            # عرض التطابقات مع الحد الأقصى
            display_count = min(len(matches), MAX_MATCHES_DISPLAY)
            for i, match in enumerate(matches[:display_count], 1):
                print(f"   {i:2d}. {match}")
            
            if len(matches) > MAX_MATCHES_DISPLAY:
                print(f"   ... و {len(matches) - MAX_MATCHES_DISPLAY} تطابق إضافي")
        
        print("="*60)
    
    def display_stats(self):
        """
        عرض الإحصائيات
        """
        runtime = datetime.now() - self.stats['start_time']
        
        print(f"\n📊 إحصائيات المراقبة:")
        print(f"⏱️  مدة التشغيل: {runtime}")
        print(f"📄 إجمالي اللصق المفحوص: {self.stats['total_pastes_checked']}")
        print(f"🎯 إجمالي اللصق مع تطابقات: {self.stats['total_matches_found']}")
        
        print(f"\n🔍 تفاصيل الأنماط:")
        for pattern_name, count in self.stats['patterns_found'].items():
            if count > 0:
                description = self.pattern_descriptions.get(pattern_name, pattern_name)
                print(f"   - {pattern_name}: {count} تطابق")
    
    def monitor(self, check_interval=None):
        """
        بدء مراقبة Pastebin
        """
        if check_interval is None:
            check_interval = DEFAULT_CHECK_INTERVAL
        
        # في الوضع التجريبي، استخدم فترة أقصر
        if self.demo_mode:
            check_interval = min(check_interval, 30)
        
        print("🔍 بدء مراقبة Pastebin المحسن")
        if self.demo_mode:
            print("🧪 الوضع: تجريبي (بيانات وهمية)")
        else:
            print(f"🔑 استخدام API Key: {self.api_key[:8] if self.api_key else 'غير محدد'}...")
        
        print(f"⏱️  فترة التحقق: {check_interval} ثانية")
        print("📋 الأنماط المراقبة:")
        for pattern_name, description in self.pattern_descriptions.items():
            print(f"   - {pattern_name}: {description}")
        print("-" * 60)
        
        try:
            while True:
                print(f"\n🔄 فحص جديد في {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                
                # الحصول على اللصق (تجريبي أو حقيقي)
                if self.demo_mode:
                    recent_pastes = self.get_sample_pastes()
                else:
                    # هنا يمكن إضافة الكود للحصول على اللصق الحقيقي
                    recent_pastes = []
                
                if not recent_pastes:
                    print("ℹ️  لا توجد لصق جديدة للفحص")
                else:
                    print(f"📄 تم العثور على {len(recent_pastes)} لصق للفحص")
                
                # فحص كل لصق
                for paste_info in recent_pastes:
                    paste_id = paste_info['id']
                    
                    # تجنب معالجة نفس اللصق مرتين
                    if paste_id in self.processed_pastes:
                        continue
                    
                    self.processed_pastes.add(paste_id)
                    self.stats['total_pastes_checked'] += 1
                    
                    # الحصول على المحتوى
                    if self.demo_mode:
                        content = self.get_sample_content(paste_id)
                    else:
                        # هنا يمكن إضافة الكود للحصول على المحتوى الحقيقي
                        content = ""
                    
                    if content:
                        # البحث عن الأنماط
                        found_patterns = self.search_patterns(content)
                        
                        if found_patterns:
                            self.display_match(paste_info, found_patterns)
                        else:
                            print(f"   ℹ️  لا توجد أنماط في {paste_id}")
                    
                    # توقف قصير بين اللصق
                    if SECURITY_SETTINGS['respect_rate_limits']:
                        time.sleep(SECURITY_SETTINGS['min_request_interval'])
                
                # عرض الإحصائيات كل 5 دورات
                if self.stats['total_pastes_checked'] % 10 == 0 and self.stats['total_pastes_checked'] > 0:
                    self.display_stats()
                
                # انتظار قبل الفحص التالي
                print(f"\n⏳ انتظار {check_interval} ثانية قبل الفحص التالي...")
                time.sleep(check_interval)
                
        except KeyboardInterrupt:
            print("\n🛑 تم إيقاف المراقبة بواسطة المستخدم")
            self.display_stats()
        except Exception as e:
            print(f"\n❌ خطأ غير متوقع: {e}")

def main():
    """
    الدالة الرئيسية
    """
    print("🚀 مراقب Pastebin المحسن للأغراض البحثية")
    print("=" * 50)
    
    # التحقق من وجود أنماط مفعلة
    enabled_patterns = get_enabled_patterns()
    if not enabled_patterns:
        print("❌ لا توجد أنماط مفعلة!")
        print("ℹ️  يرجى تحرير ملف config.py لتفعيل الأنماط المطلوبة")
        return
    
    print("ℹ️  هذا إصدار محسن يستخدم ملف الإعدادات")
    print("ℹ️  يمكنك تخصيص الأنماط في ملف config.py")
    print("=" * 50)
    
    # إنشاء مثيل المراقب
    monitor = EnhancedPastebinMonitor(demo_mode=True)
    
    # بدء المراقبة
    monitor.monitor()

if __name__ == "__main__":
    main()
