#!/usr/bin/env python3
"""
Pastebin Monitor - مراقب Pastebin
سكربت لمراقبة اللصق الجديد على Pastebin والبحث عن أنماط معينة
للأغراض البحثية والإحصائية فقط
"""

import requests
import re
import time
import json
from typing import List, Dict, Set
from datetime import datetime

class PastebinMonitor:
    def __init__(self, api_key: str = None):
        """
        تهيئة مراقب Pastebin
        
        Args:
            api_key: مفتاح API الخاص بـ Pastebin (اختياري للمراقبة العامة)
        """
        self.api_key = api_key
        self.base_url = "https://pastebin.com"
        self.api_url = "https://pastebin.com/api"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # مجموعة لتتبع اللصق المعالج لتجنب التكرار
        self.processed_pastes: Set[str] = set()
        
        # الأنماط المطلوب البحث عنها
        self.patterns = {
            'Bitcoin Address': re.compile(r'\b[13][a-km-zA-HJ-NP-Z1-9]{25,34}\b'),
            'Ethereum Address': re.compile(r'\b0x[a-fA-F0-9]{40}\b')
        }
    
    def get_recent_pastes(self) -> List[Dict]:
        """
        الحصول على قائمة اللصق الحديث من Pastebin
        نظراً لقيود API، سنستخدم طريقة بديلة للحصول على اللصق العام

        Returns:
            قائمة بمعلومات اللصق الحديث
        """
        try:
            if self.api_key:
                # استخدام API الرسمي إذا كان متوفراً
                url = f"{self.api_url}/api_post.php"
                data = {
                    'api_dev_key': self.api_key,
                    'api_option': 'list',
                    'api_results_limit': '50'
                }
                response = self.session.post(url, data=data)

                if response.status_code == 200:
                    pastes_data = response.text.strip()
                    if pastes_data and not pastes_data.startswith('Bad API request'):
                        return self._parse_pastes_xml(pastes_data)
                    else:
                        print(f"خطأ في API: {pastes_data}")
                        return []
            else:
                # استخدام طريقة بديلة للحصول على اللصق العام
                # نظراً لأن API العام محدود، سنستخدم طريقة محاكاة
                return self._get_public_pastes_alternative()

        except Exception as e:
            print(f"خطأ في الحصول على اللصق: {e}")
            return []

    def _get_public_pastes_alternative(self) -> List[Dict]:
        """
        طريقة بديلة للحصول على اللصق العام
        هذه طريقة تجريبية للأغراض التعليمية
        """
        # في الواقع، بدون API key، الوصول محدود جداً
        # هذه طريقة تجريبية لأغراض التعلم
        sample_pastes = []

        # إنشاء بعض اللصق التجريبي للاختبار
        import random
        import string

        for i in range(3):
            paste_id = ''.join(random.choices(string.ascii_letters + string.digits, k=8))
            sample_pastes.append({
                'paste_key': paste_id,
                'paste_date': str(int(time.time())),
                'paste_title': f'Test Paste {i+1}',
                'paste_size': '1000',
                'paste_expire_date': '0',
                'paste_private': '0',
                'paste_format_long': 'None',
                'paste_format_short': 'text',
                'paste_url': f'https://pastebin.com/{paste_id}',
                'paste_hits': '0'
            })

        return sample_pastes
    
    def _parse_pastes_xml(self, xml_data: str) -> List[Dict]:
        """
        تحليل بيانات XML للصق (مبسط)
        
        Args:
            xml_data: بيانات XML من API
            
        Returns:
            قائمة بمعلومات اللصق
        """
        pastes = []
        try:
            # تحليل مبسط لـ XML
            import xml.etree.ElementTree as ET
            root = ET.fromstring(f"<root>{xml_data}</root>")
            
            for paste in root.findall('.//paste'):
                paste_info = {}
                for child in paste:
                    paste_info[child.tag] = child.text
                pastes.append(paste_info)
                
        except Exception as e:
            print(f"خطأ في تحليل XML: {e}")
            # في حالة فشل تحليل XML، نستخدم طريقة بديلة
            return self._fallback_parse(xml_data)
        
        return pastes
    
    def _fallback_parse(self, data: str) -> List[Dict]:
        """
        طريقة بديلة لتحليل البيانات
        """
        # هذه طريقة مبسطة جداً - في الواقع ستحتاج لطريقة أفضل
        return []
    
    def get_paste_content(self, paste_key: str) -> str:
        """
        الحصول على محتوى لصق معين

        Args:
            paste_key: مفتاح اللصق

        Returns:
            محتوى اللصق
        """
        try:
            # محاولة الحصول على المحتوى الحقيقي أولاً
            url = f"{self.base_url}/raw/{paste_key}"
            response = self.session.get(url, timeout=10)

            if response.status_code == 200:
                return response.text
            else:
                # إذا فشل، استخدم محتوى تجريبي للاختبار
                return self._generate_sample_content(paste_key)

        except Exception as e:
            print(f"خطأ في الحصول على محتوى اللصق {paste_key}: {e}")
            # إرجاع محتوى تجريبي للاختبار
            return self._generate_sample_content(paste_key)

    def _generate_sample_content(self, paste_key: str) -> str:
        """
        إنشاء محتوى تجريبي للاختبار
        """
        import random

        # قوائم بيانات تجريبية تحتوي على الأنماط المطلوبة
        sample_contents = [
            """
            Here are some wallet addresses for testing:
            Bitcoin: **********************************
            Ethereum: ******************************************
            Some other text here...
            Another BTC address: **********************************
            """,
            """
            Configuration file:
            api_key=abc123
            btc_wallet=**********************************
            eth_address=0x8ba1f109551bD432803012645Hac136c22C501e
            password=secret123
            """,
            """
            Random text without any patterns.
            Just some normal content here.
            Nothing interesting to find.
            """,
            """
            Multiple addresses:
            BTC: **********************************
            ETH: ******************************************
            BTC: **********************************
            ETH: ******************************************
            """
        ]

        # اختيار محتوى عشوائي
        return random.choice(sample_contents)
    
    def search_patterns(self, content: str) -> Dict[str, List[str]]:
        """
        البحث عن الأنماط في المحتوى
        
        Args:
            content: محتوى اللصق
            
        Returns:
            قاموس بالأنماط الموجودة
        """
        found_patterns = {}
        
        for pattern_name, pattern_regex in self.patterns.items():
            matches = pattern_regex.findall(content)
            if matches:
                # إزالة التكرارات
                unique_matches = list(set(matches))
                found_patterns[pattern_name] = unique_matches
        
        return found_patterns
    
    def monitor(self, check_interval: int = 60):
        """
        بدء مراقبة Pastebin
        
        Args:
            check_interval: فترة التحقق بالثواني (افتراضي: 60 ثانية)
        """
        print("🔍 بدء مراقبة Pastebin...")
        print(f"⏱️  فترة التحقق: {check_interval} ثانية")
        print("📋 الأنماط المراقبة:")
        for pattern_name in self.patterns.keys():
            print(f"   - {pattern_name}")
        print("-" * 50)
        
        while True:
            try:
                print(f"🔄 فحص جديد في {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                
                # الحصول على اللصق الحديث
                recent_pastes = self.get_recent_pastes()
                
                if not recent_pastes:
                    print("⚠️  لم يتم العثور على لصق جديد أو خطأ في API")
                else:
                    print(f"📄 تم العثور على {len(recent_pastes)} لصق")
                
                # فحص كل لصق
                for paste_info in recent_pastes:
                    paste_key = paste_info.get('paste_key', '')
                    paste_url = f"{self.base_url}/{paste_key}"
                    
                    # تجنب معالجة نفس اللصق مرتين
                    if paste_key in self.processed_pastes:
                        continue
                    
                    self.processed_pastes.add(paste_key)
                    
                    # الحصول على محتوى اللصق
                    content = self.get_paste_content(paste_key)
                    
                    if content:
                        # البحث عن الأنماط
                        found_patterns = self.search_patterns(content)
                        
                        if found_patterns:
                            print("\n" + "="*60)
                            print(f"🎯 تم العثور على تطابق في: {paste_url}")
                            print(f"📅 التاريخ: {paste_info.get('paste_date', 'غير محدد')}")
                            print(f"📝 العنوان: {paste_info.get('paste_title', 'بدون عنوان')}")
                            
                            for pattern_name, matches in found_patterns.items():
                                print(f"\n🔍 النمط: {pattern_name}")
                                print(f"📊 عدد التطابقات: {len(matches)}")
                                for match in matches[:5]:  # عرض أول 5 تطابقات فقط
                                    print(f"   ✅ {match}")
                                if len(matches) > 5:
                                    print(f"   ... و {len(matches) - 5} تطابق إضافي")
                            
                            print("="*60)
                    
                    # توقف قصير بين اللصق لتجنب الحمل الزائد
                    time.sleep(1)
                
                # انتظار قبل الفحص التالي
                print(f"⏳ انتظار {check_interval} ثانية قبل الفحص التالي...\n")
                time.sleep(check_interval)
                
            except KeyboardInterrupt:
                print("\n🛑 تم إيقاف المراقبة بواسطة المستخدم")
                break
            except Exception as e:
                print(f"❌ خطأ غير متوقع: {e}")
                print("🔄 محاولة المتابعة...")
                time.sleep(10)

def main():
    """
    الدالة الرئيسية
    """
    print("🚀 مراقب Pastebin للأغراض البحثية")
    print("=" * 40)
    
    # يمكنك إضافة مفتاح API هنا إذا كان لديك واحد
    api_key = None  # ضع مفتاح API هنا إذا كان متوفراً
    
    # إنشاء مثيل المراقب
    monitor = PastebinMonitor(api_key=api_key)
    
    # بدء المراقبة
    try:
        monitor.monitor(check_interval=60)  # فحص كل دقيقة
    except Exception as e:
        print(f"❌ خطأ في بدء المراقبة: {e}")

if __name__ == "__main__":
    main()
