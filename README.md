# مراقب Pastebin للأغراض البحثية

## الوصف

هذا المشروع يحتوي على سكربتات Python مصممة للأغراض البحثية والأكاديمية لمراقبة المحتوى العام على Pastebin والبحث عن أنماط معينة من البيانات. يهدف إلى توفير إحصائيات حول أنواع البيانات التي قد تتسرب عن طريق الخطأ على المنصات العامة.

## الملفات المتوفرة

### 1. `simple_pastebin_monitor.py` - الإصدار التجريبي

- **الغرض**: للاختبار والتعلم
- **البيانات**: يستخدم بيانات وهمية للاختبار
- **المتطلبات**: لا يحتاج مفتاح API
- **الاستخدام**: مثالي لفهم كيفية عمل السكربت

### 2. `enhanced_monitor.py` - الإصدار المحسن

- **الغرض**: نسخة متقدمة مع إعدادات قابلة للتخصيص
- **الميزات**: إحصائيات، أنماط متعددة، عرض محسن
- **الإعدادات**: يستخدم ملف `config.py` للتخصيص

### 3. `real_pastebin_monitor.py` - الإصدار الحقيقي

- **الغرض**: للاستخدام الفعلي مع Pastebin
- **البيانات**: يتصل بـ API الحقيقي لـ Pastebin
- **المتطلبات**: يحتاج مفتاح API صالح

### 4. `test_patterns.py` - اختبار الأنماط

- **الغرض**: اختبار صحة التعبيرات النمطية
- **الاستخدام**: للتأكد من دقة الأنماط المستخدمة

### 5. `config.py` - ملف الإعدادات

- **الغرض**: تخصيص الأنماط والإعدادات
- **الميزات**: تفعيل/إلغاء تفعيل الأنماط، إعدادات العرض

## الميزات

- مراقبة اللصق الجديد على Pastebin
- البحث عن أنماط محددة باستخدام التعبيرات النمطية
- عرض النتائج في الطرفية مع روابط اللصق
- تجنب معالجة نفس اللصق مرتين
- واجهة باللغة العربية
- إحصائيات مفصلة
- أنماط قابلة للتخصيص

## المتطلبات

- Python 3.6+
- مكتبة requests

## التثبيت

```bash
pip install -r requirements.txt
```

## الاستخدام

### للمبتدئين:

```bash
py simple_pastebin_monitor.py
```

### للاستخدام المتقدم:

```bash
py enhanced_monitor.py
```

### للاستخدام الحقيقي مع API:

```bash
py real_pastebin_monitor.py
```

### لاختبار الأنماط:

```bash
py test_patterns.py
```

## الأنماط المراقبة

### الأنماط المفعلة افتراضياً:

- ✅ **Bitcoin Address**: عناوين Bitcoin العامة
- ✅ **Ethereum Address**: عناوين Ethereum

### الأنماط المتاحة (معطلة افتراضياً):

- ⚪ **Email Address**: عناوين البريد الإلكتروني
- ⚪ **IP Address**: عناوين IP
- ⚪ **Credit Card**: أرقام بطاقات ائتمان محتملة
- ⚪ **Phone Number**: أرقام هواتف
- ⚪ **API Key Pattern**: مفاتيح API محتملة
- ⚪ **SSH Private Key**: مفاتيح SSH خاصة
- ⚪ **AWS Access Key**: مفاتيح وصول AWS

## تخصيص الأنماط

يمكنك تخصيص الأنماط المراقبة عبر تحرير ملف `config.py`:

```python
# تفعيل نمط معين
SEARCH_PATTERNS['Email Address']['enabled'] = True

# إضافة نمط جديد
SEARCH_PATTERNS['Custom Pattern'] = {
    'pattern': re.compile(r'your_regex_here'),
    'description': 'وصف النمط',
    'enabled': True
}
```

## إضافة مفتاح API (للاستخدام الحقيقي)

للحصول على نتائج أفضل مع البيانات الحقيقية:

1. إنشاء حساب على Pastebin
2. الحصول على مفتاح API من https://pastebin.com/doc_api
3. استخدام `real_pastebin_monitor.py` مع المفتاح

## ملاحظات مهمة

- هذا السكربت للأغراض البحثية والتعليمية فقط
- يراقب المحتوى العام فقط
- لا يحفظ أي بيانات حساسة
- يعرض فقط الأنماط الموجودة وروابط اللصق
- يحترم حدود استخدام API
- الأنماط الحساسة معطلة افتراضياً لأسباب أخلاقية

## الأمان والأخلاقيات

- استخدم هذه الأدوات بمسؤولية
- احترم خصوصية الآخرين
- لا تستخدم البيانات المكتشفة لأغراض ضارة
- اتبع القوانين المحلية والدولية

## إخلاء المسؤولية

هذا السكربت مخصص للأغراض التعليمية والبحثية فقط. المستخدم مسؤول عن الامتثال لجميع القوانين واللوائح المحلية والدولية المعمول بها. المطور غير مسؤول عن أي استخدام غير قانوني أو غير أخلاقي لهذه الأدوات.
