# ملخص مشروع مراقب Pastebin

## نظرة عامة
تم إنشاء مجموعة شاملة من السكربتات Python لمراقبة وتحليل البيانات المتسربة على منصة Pastebin للأغراض البحثية والأكاديمية.

## الملفات المُنشأة

### 1. السكربتات الرئيسية

#### `simple_pastebin_monitor.py` - الإصدار التجريبي
- **الغرض**: للتعلم والاختبار
- **الميزات**: 
  - بيانات وهمية للاختبار
  - لا يحتاج مفتاح API
  - واجهة بسيطة وواضحة
- **الاستخدام**: `py simple_pastebin_monitor.py`

#### `enhanced_monitor.py` - الإصدار المحسن
- **الغرض**: نسخة متقدمة مع إعدادات قابلة للتخصيص
- **الميزات**:
  - إحصائيات مفصلة
  - أنماط متعددة قابلة للتخصيص
  - عرض محسن مع معلومات إضافية
  - يستخدم ملف الإعدادات
- **الاستخدام**: `py enhanced_monitor.py`

#### `real_pastebin_monitor.py` - الإصدار الحقيقي
- **الغرض**: للاستخدام الفعلي مع API Pastebin
- **الميزات**:
  - اتصال حقيقي بـ API
  - معالجة أخطاء شاملة
  - احترام حدود API
- **المتطلبات**: مفتاح API صالح
- **الاستخدام**: `py real_pastebin_monitor.py`

#### `run.py` - سكربت التشغيل السريع
- **الغرض**: قائمة تفاعلية لاختيار الإصدار المناسب
- **الميزات**:
  - واجهة سهلة الاستخدام
  - عرض الإعدادات والأنماط
  - تشغيل مباشر للسكربتات
- **الاستخدام**: `py run.py`

### 2. ملفات الإعدادات والاختبار

#### `config.py` - ملف الإعدادات
- **الغرض**: تخصيص الأنماط والإعدادات
- **الميزات**:
  - 9 أنماط مختلفة (2 مفعل افتراضياً)
  - إعدادات API والعرض والأمان
  - دوال للتحكم في الأنماط
- **الأنماط المتاحة**:
  - ✅ Bitcoin Address
  - ✅ Ethereum Address
  - ⚪ Email Address (معطل)
  - ⚪ IP Address (معطل)
  - ⚪ Credit Card (معطل)
  - ⚪ Phone Number (معطل)
  - ⚪ API Key Pattern (معطل)
  - ⚪ SSH Private Key (معطل)
  - ⚪ AWS Access Key (معطل)

#### `test_patterns.py` - اختبار الأنماط
- **الغرض**: التحقق من صحة التعبيرات النمطية
- **الميزات**:
  - اختبار شامل للأنماط
  - عرض النتائج المتوقعة مقابل الفعلية
  - اختبار على نص مختلط
- **الاستخدام**: `py test_patterns.py`

### 3. ملفات التوثيق

#### `README.md` - دليل المستخدم
- **المحتوى**:
  - شرح شامل لجميع الملفات
  - تعليمات التثبيت والاستخدام
  - أمثلة على التخصيص
  - ملاحظات الأمان والأخلاقيات

#### `requirements.txt` - المتطلبات
- **المحتوى**: `requests>=2.31.0`

#### `PROJECT_SUMMARY.md` - هذا الملف
- **المحتوى**: ملخص شامل للمشروع

## الأنماط المراقبة

### الأنماط المفعلة افتراضياً:
1. **Bitcoin Address**: `\b[13][a-km-zA-HJ-NP-Z1-9]{26,34}\b`
   - يكشف عناوين Bitcoin العامة
   - يدعم العناوين التقليدية والـ P2SH

2. **Ethereum Address**: `\b0x[a-fA-F0-9]{40}\b`
   - يكشف عناوين Ethereum
   - يتطلب البادئة 0x

### الأنماط المعطلة (للأمان):
- عناوين البريد الإلكتروني
- عناوين IP
- أرقام بطاقات ائتمان
- أرقام هواتف
- مفاتيح API
- مفاتيح SSH خاصة
- مفاتيح وصول AWS

## الميزات الرئيسية

### 1. التصميم المرن
- أنماط قابلة للتخصيص
- إعدادات منفصلة
- دعم إضافة أنماط جديدة

### 2. الأمان والأخلاقيات
- الأنماط الحساسة معطلة افتراضياً
- احترام حدود API
- عدم حفظ البيانات الحساسة

### 3. سهولة الاستخدام
- واجهة باللغة العربية
- قائمة تفاعلية للتشغيل
- رسائل خطأ واضحة

### 4. الموثوقية
- معالجة شاملة للأخطاء
- إعادة المحاولة عند الفشل
- تجنب التكرار

## طرق الاستخدام

### للمبتدئين:
```bash
py run.py
# اختر الخيار 1 (الإصدار التجريبي)
```

### للمستخدمين المتقدمين:
```bash
py enhanced_monitor.py
```

### للاستخدام الحقيقي:
```bash
py real_pastebin_monitor.py
```

### لاختبار الأنماط:
```bash
py test_patterns.py
```

## التخصيص

### إضافة نمط جديد:
```python
# في ملف config.py
SEARCH_PATTERNS['New Pattern'] = {
    'pattern': re.compile(r'your_regex_here'),
    'description': 'وصف النمط',
    'enabled': True
}
```

### تفعيل نمط معطل:
```python
# في ملف config.py
SEARCH_PATTERNS['Email Address']['enabled'] = True
```

## الاعتبارات الأخلاقية

### ✅ مسموح:
- البحث الأكاديمي
- الإحصائيات العامة
- تحليل أنماط التسريب

### ❌ غير مسموح:
- استخدام البيانات المكتشفة
- انتهاك الخصوصية
- الأنشطة غير القانونية

## المتطلبات التقنية

- **Python**: 3.6 أو أحدث
- **المكتبات**: requests
- **نظام التشغيل**: Windows, Linux, macOS
- **مفتاح API**: مطلوب للاستخدام الحقيقي فقط

## الخلاصة

تم إنشاء مجموعة شاملة ومرنة من الأدوات لمراقبة وتحليل البيانات المتسربة على Pastebin. المشروع يوازن بين الفائدة البحثية والاعتبارات الأخلاقية، مع توفير أدوات سهلة الاستخدام ومرنة للباحثين والأكاديميين.

---

**تاريخ الإنشاء**: 2025-08-07  
**الغرض**: البحث الأكاديمي والتعليمي  
**الحالة**: جاهز للاستخدام
