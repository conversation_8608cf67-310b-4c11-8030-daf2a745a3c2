#!/usr/bin/env python3
"""
مراقب Pastebin الحقيقي - للأغراض البحثية والتعليمية
سكربت لمراقبة أنماط البيانات على Pastebin باستخدام API الحقيقي
"""

import requests
import re
import time
import xml.etree.ElementTree as ET
from datetime import datetime

class RealPastebinMonitor:
    def __init__(self, api_key):
        """
        تهيئة مراقب Pastebin الحقيقي
        
        Args:
            api_key: مفتاح API الخاص بـ Pastebin (مطلوب)
        """
        if not api_key:
            raise ValueError("مفتاح API مطلوب للاستخدام الحقيقي")
        
        self.api_key = api_key
        self.base_url = "https://pastebin.com"
        self.api_url = "https://pastebin.com/api"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'PastebinMonitor/1.0 (Research Purpose)'
        })
        
        # الأنماط المطلوب البحث عنها
        self.patterns = {
            'Bitcoin Address': re.compile(r'\b[13][a-km-zA-HJ-NP-Z1-9]{26,34}\b'),
            'Ethereum Address': re.compile(r'\b0x[a-fA-F0-9]{40}\b')
        }
        
        # مجموعة لتتبع اللصق المعالج
        self.processed_pastes = set()
    
    def get_recent_pastes(self):
        """
        الحصول على قائمة اللصق الحديث من Pastebin API
        
        Returns:
            قائمة بمعلومات اللصق الحديث
        """
        try:
            url = f"{self.api_url}/api_post.php"
            data = {
                'api_dev_key': self.api_key,
                'api_option': 'list',
                'api_results_limit': '50'  # الحد الأقصى 50
            }
            
            response = self.session.post(url, data=data, timeout=30)
            
            if response.status_code == 200:
                response_text = response.text.strip()
                
                # التحقق من الأخطاء
                if response_text.startswith('Bad API request'):
                    print(f"❌ خطأ في API: {response_text}")
                    return []
                elif response_text == 'No pastes found.':
                    print("ℹ️  لا توجد لصق جديدة")
                    return []
                else:
                    return self._parse_pastes_xml(response_text)
            else:
                print(f"❌ خطأ HTTP: {response.status_code}")
                return []
                
        except requests.exceptions.RequestException as e:
            print(f"❌ خطأ في الشبكة: {e}")
            return []
        except Exception as e:
            print(f"❌ خطأ غير متوقع: {e}")
            return []
    
    def _parse_pastes_xml(self, xml_data):
        """
        تحليل بيانات XML للصق
        
        Args:
            xml_data: بيانات XML من API
            
        Returns:
            قائمة بمعلومات اللصق
        """
        pastes = []
        try:
            # إضافة عنصر جذر للـ XML
            root_xml = f"<pastes>{xml_data}</pastes>"
            root = ET.fromstring(root_xml)
            
            for paste in root.findall('paste'):
                paste_info = {}
                for child in paste:
                    paste_info[child.tag] = child.text if child.text else ""
                pastes.append(paste_info)
                
        except ET.ParseError as e:
            print(f"❌ خطأ في تحليل XML: {e}")
        except Exception as e:
            print(f"❌ خطأ في معالجة البيانات: {e}")
        
        return pastes
    
    def get_paste_content(self, paste_key):
        """
        الحصول على محتوى لصق معين
        
        Args:
            paste_key: مفتاح اللصق
            
        Returns:
            محتوى اللصق
        """
        try:
            url = f"{self.base_url}/raw/{paste_key}"
            response = self.session.get(url, timeout=15)
            
            if response.status_code == 200:
                return response.text
            elif response.status_code == 404:
                print(f"⚠️  اللصق {paste_key} غير موجود أو محذوف")
                return ""
            else:
                print(f"⚠️  لا يمكن الوصول للصق {paste_key}: HTTP {response.status_code}")
                return ""
                
        except requests.exceptions.RequestException as e:
            print(f"❌ خطأ في الشبكة للصق {paste_key}: {e}")
            return ""
        except Exception as e:
            print(f"❌ خطأ في الحصول على محتوى اللصق {paste_key}: {e}")
            return ""
    
    def search_patterns(self, content):
        """
        البحث عن الأنماط في المحتوى
        
        Args:
            content: محتوى اللصق
            
        Returns:
            قاموس بالأنماط الموجودة
        """
        found_patterns = {}
        
        for pattern_name, pattern_regex in self.patterns.items():
            matches = pattern_regex.findall(content)
            if matches:
                # إزالة التكرارات
                unique_matches = list(set(matches))
                found_patterns[pattern_name] = unique_matches
        
        return found_patterns
    
    def monitor(self, check_interval=120):
        """
        بدء مراقبة Pastebin
        
        Args:
            check_interval: فترة التحقق بالثواني (افتراضي: 120 ثانية)
        """
        print("🔍 بدء مراقبة Pastebin الحقيقي")
        print(f"🔑 استخدام API Key: {self.api_key[:8]}...")
        print(f"⏱️  فترة التحقق: {check_interval} ثانية")
        print("📋 الأنماط المراقبة:")
        for pattern_name in self.patterns.keys():
            print(f"   - {pattern_name}")
        print("⚠️  تذكير: احترم حدود استخدام API")
        print("-" * 60)
        
        try:
            while True:
                print(f"\n🔄 فحص جديد في {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                
                # الحصول على اللصق الحديث
                recent_pastes = self.get_recent_pastes()
                
                if not recent_pastes:
                    print("ℹ️  لا توجد لصق جديدة للفحص")
                else:
                    print(f"📄 تم العثور على {len(recent_pastes)} لصق للفحص")
                
                # فحص كل لصق
                for paste_info in recent_pastes:
                    paste_key = paste_info.get('paste_key', '')
                    paste_url = paste_info.get('paste_url', f"{self.base_url}/{paste_key}")
                    
                    # تجنب معالجة نفس اللصق مرتين
                    if paste_key in self.processed_pastes:
                        continue
                    
                    self.processed_pastes.add(paste_key)
                    
                    # الحصول على محتوى اللصق
                    content = self.get_paste_content(paste_key)
                    
                    if content:
                        # البحث عن الأنماط
                        found_patterns = self.search_patterns(content)
                        
                        if found_patterns:
                            print("\n" + "="*60)
                            print(f"🎯 تم العثور على تطابق في: {paste_url}")
                            print(f"📅 التاريخ: {paste_info.get('paste_date', 'غير محدد')}")
                            print(f"📝 العنوان: {paste_info.get('paste_title', 'بدون عنوان')}")
                            print(f"📏 الحجم: {paste_info.get('paste_size', 'غير محدد')} بايت")
                            
                            for pattern_name, matches in found_patterns.items():
                                print(f"\n🔍 النمط: {pattern_name}")
                                print(f"📊 عدد التطابقات: {len(matches)}")
                                for i, match in enumerate(matches[:10], 1):  # عرض أول 10 تطابقات فقط
                                    print(f"   {i:2d}. {match}")
                                if len(matches) > 10:
                                    print(f"   ... و {len(matches) - 10} تطابق إضافي")
                            
                            print("="*60)
                    
                    # توقف قصير بين اللصق لتجنب الحمل الزائد
                    time.sleep(2)
                
                # انتظار قبل الفحص التالي
                print(f"\n⏳ انتظار {check_interval} ثانية قبل الفحص التالي...")
                time.sleep(check_interval)
                
        except KeyboardInterrupt:
            print("\n🛑 تم إيقاف المراقبة بواسطة المستخدم")
        except Exception as e:
            print(f"\n❌ خطأ غير متوقع: {e}")
            print("🔄 يُنصح بإعادة تشغيل السكربت")

def main():
    """
    الدالة الرئيسية
    """
    print("🚀 مراقب Pastebin الحقيقي للأغراض البحثية")
    print("=" * 50)
    
    # طلب مفتاح API من المستخدم
    api_key = input("🔑 أدخل مفتاح API الخاص بك: ").strip()
    
    if not api_key:
        print("❌ مفتاح API مطلوب!")
        print("ℹ️  يمكنك الحصول على مفتاح API من: https://pastebin.com/doc_api")
        return
    
    # إنشاء مثيل المراقب
    try:
        monitor = RealPastebinMonitor(api_key=api_key)
        
        # بدء المراقبة
        monitor.monitor(check_interval=120)  # فحص كل دقيقتين
        
    except ValueError as e:
        print(f"❌ خطأ في التكوين: {e}")
    except Exception as e:
        print(f"❌ خطأ في بدء المراقبة: {e}")

if __name__ == "__main__":
    main()
