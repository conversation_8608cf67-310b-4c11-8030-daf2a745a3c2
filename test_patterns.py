#!/usr/bin/env python3
"""
اختبار أنماط التعبيرات النمطية
سكربت لاختبار صحة الأنماط المستخدمة في مراقب Pastebin
"""

import re

def test_patterns():
    """
    اختبار أنماط Bitcoin و Ethereum
    """
    # تعريف الأنماط
    patterns = {
        'Bitcoin Address': re.compile(r'\b[13][a-km-zA-HJ-NP-Z1-9]{25,34}\b'),
        'Ethereum Address': re.compile(r'\b0x[a-fA-F0-9]{40}\b')
    }
    
    # بيانات اختبار
    test_data = {
        'Bitcoin Addresses - صحيحة': [
            '**********************************',  # Genesis block address
            '**********************************',
            '**********************************',  # P2SH address
            '**********************************',
            '**********************************'
        ],
        'Bitcoin Addresses - خاطئة': [
            '0A1zP1eP5QGefi2DMPTfTL5SLmv7DivfNa',  # يبدأ بـ 0
            '2A1zP1eP5QGefi2DMPTfTL5SLmv7DivfNa',  # يبدأ بـ 2
            '*********************************',   # قصير جداً
            '**********************************XXXXXXXXX',  # طويل جداً
            '*********************************0',  # يحتوي على 0
            '*********************************O'   # يحتوي على O
        ],
        'Ethereum Addresses - صحيحة': [
            '******************************************',
            '0x8ba1f109551bD432803012645Hac136c22C501e',
            '******************************************',
            '******************************************',
            '******************************************'
        ],
        'Ethereum Addresses - خاطئة': [
            '742d35Cc6634C0532925a3b8D4C9db96590c4C5d',   # بدون 0x
            '0x742d35Cc6634C0532925a3b8D4C9db96590c4C5',   # قصير
            '******************************************X', # طويل
            '0x742d35Cc6634C0532925a3b8D4C9db96590c4C5G',  # حرف غير صحيح
            '0X742d35Cc6634C0532925a3b8D4C9db96590c4C5d'   # X كبير
        ]
    }
    
    # نص تجريبي يحتوي على عناوين مختلطة
    sample_text = """
    Here's a configuration file with various addresses:
    
    Bitcoin addresses:
    - Main wallet: **********************************
    - Backup wallet: **********************************
    - Invalid: 0A1zP1eP5QGefi2DMPTfTL5SLmv7DivfNa (should not match)
    
    Ethereum addresses:
    - Contract: ******************************************
    - Wallet: 0x8ba1f109551bD432803012645Hac136c22C501e
    - Invalid: 742d35Cc6634C0532925a3b8D4C9db96590c4C5d (should not match)
    
    Some random text and numbers: 12345, abc123, <EMAIL>
    """
    
    print("🧪 اختبار أنماط التعبيرات النمطية")
    print("=" * 50)
    
    # اختبار كل نمط على البيانات التجريبية
    for category, addresses in test_data.items():
        print(f"\n📋 اختبار: {category}")
        print("-" * 30)
        
        if 'Bitcoin' in category:
            pattern = patterns['Bitcoin Address']
        elif 'Ethereum' in category:
            pattern = patterns['Ethereum Address']
        else:
            continue
        
        for address in addresses:
            match = pattern.search(address)
            status = "✅ تطابق" if match else "❌ لا يطابق"
            expected = "✅ متوقع" if 'صحيحة' in category else "❌ متوقع"
            
            if ('صحيحة' in category and match) or ('خاطئة' in category and not match):
                result = "🎯 صحيح"
            else:
                result = "⚠️ خطأ"
            
            print(f"  {address[:20]}... -> {status} | {expected} | {result}")
    
    # اختبار النص المختلط
    print(f"\n📄 اختبار النص المختلط:")
    print("-" * 30)
    
    for pattern_name, pattern_regex in patterns.items():
        matches = pattern_regex.findall(sample_text)
        print(f"\n🔍 {pattern_name}:")
        print(f"📊 عدد التطابقات: {len(matches)}")
        for i, match in enumerate(matches, 1):
            print(f"  {i}. {match}")
    
    print("\n" + "=" * 50)
    print("✅ انتهى الاختبار")

if __name__ == "__main__":
    test_patterns()
