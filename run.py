#!/usr/bin/env python3
"""
سكربت تشغيل سريع لمراقب Pastebin
يوفر قائمة تفاعلية لاختيار الإصدار المناسب
"""

import os
import sys
import subprocess
from config import list_all_patterns, validate_config

def clear_screen():
    """مسح الشاشة"""
    os.system('cls' if os.name == 'nt' else 'clear')

def show_menu():
    """عرض القائمة الرئيسية"""
    clear_screen()
    print("🚀 مراقب Pastebin للأغراض البحثية")
    print("=" * 50)
    print("اختر الإصدار المناسب:")
    print()
    print("1. 🧪 الإصدار التجريبي (simple_pastebin_monitor.py)")
    print("   - للاختبار والتعلم")
    print("   - بيانات وهمية")
    print("   - لا يحتاج API key")
    print()
    print("2. ⚡ الإصدار المحسن (enhanced_monitor.py)")
    print("   - إعدادات قابلة للتخصيص")
    print("   - إحصائيات مفصلة")
    print("   - أنماط متعددة")
    print()
    print("3. 🔑 الإصدار الحقيقي (real_pastebin_monitor.py)")
    print("   - يتطلب API key")
    print("   - بيانات حقيقية من Pastebin")
    print("   - للاستخدام الفعلي")
    print()
    print("4. 🧪 اختبار الأنماط (test_patterns.py)")
    print("   - اختبار صحة التعبيرات النمطية")
    print("   - التحقق من دقة الأنماط")
    print()
    print("5. ⚙️  عرض الإعدادات الحالية")
    print("6. 📋 عرض الأنماط المتاحة")
    print("0. ❌ خروج")
    print()
    print("=" * 50)

def run_script(script_name):
    """تشغيل سكربت معين"""
    try:
        print(f"🚀 تشغيل {script_name}...")
        print("=" * 30)
        
        # استخدام py بدلاً من python في Windows
        python_cmd = 'py' if os.name == 'nt' else 'python3'
        
        # تشغيل السكربت
        result = subprocess.run([python_cmd, script_name], 
                              capture_output=False, 
                              text=True)
        
        print("\n" + "=" * 30)
        if result.returncode == 0:
            print("✅ انتهى التشغيل بنجاح")
        else:
            print(f"❌ انتهى التشغيل مع خطأ (كود: {result.returncode})")
            
    except FileNotFoundError:
        print(f"❌ لم يتم العثور على الملف: {script_name}")
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف التشغيل بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في التشغيل: {e}")
    
    input("\n📱 اضغط Enter للعودة للقائمة الرئيسية...")

def show_current_config():
    """عرض الإعدادات الحالية"""
    clear_screen()
    print("⚙️  الإعدادات الحالية")
    print("=" * 40)
    
    try:
        from config import (
            DEFAULT_CHECK_INTERVAL, 
            DEFAULT_RESULTS_LIMIT, 
            MAX_MATCHES_DISPLAY,
            API_SETTINGS,
            DISPLAY_SETTINGS,
            SECURITY_SETTINGS
        )
        
        print(f"⏱️  فترة التحقق الافتراضية: {DEFAULT_CHECK_INTERVAL} ثانية")
        print(f"📊 حد النتائج: {DEFAULT_RESULTS_LIMIT}")
        print(f"🔍 الحد الأقصى لعرض التطابقات: {MAX_MATCHES_DISPLAY}")
        print()
        
        print("🌐 إعدادات API:")
        print(f"   - المهلة الزمنية: {API_SETTINGS['timeout']} ثانية")
        print(f"   - محاولات إعادة المحاولة: {API_SETTINGS['retry_attempts']}")
        print()
        
        print("🎨 إعدادات العرض:")
        print(f"   - عرض الطوابع الزمنية: {'نعم' if DISPLAY_SETTINGS['show_timestamps'] else 'لا'}")
        print(f"   - عرض حجم اللصق: {'نعم' if DISPLAY_SETTINGS['show_paste_size'] else 'لا'}")
        print(f"   - الحد الأقصى لطول العنوان: {DISPLAY_SETTINGS['max_title_length']}")
        print()
        
        print("🔒 إعدادات الأمان:")
        print(f"   - احترام حدود المعدل: {'نعم' if SECURITY_SETTINGS['respect_rate_limits'] else 'لا'}")
        print(f"   - الحد الأدنى بين الطلبات: {SECURITY_SETTINGS['min_request_interval']} ثانية")
        print(f"   - تخطي اللصق الكبيرة: {'نعم' if SECURITY_SETTINGS['skip_large_pastes'] else 'لا'}")
        
        print("\n" + "=" * 40)
        validate_config()
        
    except ImportError as e:
        print(f"❌ خطأ في تحميل الإعدادات: {e}")
    
    input("\n📱 اضغط Enter للعودة للقائمة الرئيسية...")

def show_available_patterns():
    """عرض الأنماط المتاحة"""
    clear_screen()
    print("📋 الأنماط المتاحة")
    print("=" * 40)
    
    try:
        list_all_patterns()
        print("\nℹ️  يمكنك تعديل هذه الأنماط في ملف config.py")
        print("ℹ️  الأنماط المعطلة افتراضياً هي للأمان والأخلاقيات")
        
    except Exception as e:
        print(f"❌ خطأ في عرض الأنماط: {e}")
    
    input("\n📱 اضغط Enter للعودة للقائمة الرئيسية...")

def main():
    """الدالة الرئيسية"""
    while True:
        show_menu()
        
        try:
            choice = input("اختر رقماً من القائمة: ").strip()
            
            if choice == '1':
                run_script('simple_pastebin_monitor.py')
            elif choice == '2':
                run_script('enhanced_monitor.py')
            elif choice == '3':
                run_script('real_pastebin_monitor.py')
            elif choice == '4':
                run_script('test_patterns.py')
            elif choice == '5':
                show_current_config()
            elif choice == '6':
                show_available_patterns()
            elif choice == '0':
                clear_screen()
                print("👋 شكراً لاستخدام مراقب Pastebin!")
                print("🔬 نتمنى أن يكون مفيداً لمشروعك البحثي")
                break
            else:
                print("❌ اختيار غير صحيح. يرجى المحاولة مرة أخرى.")
                input("📱 اضغط Enter للمتابعة...")
                
        except KeyboardInterrupt:
            clear_screen()
            print("\n👋 تم إنهاء البرنامج")
            break
        except Exception as e:
            print(f"❌ خطأ غير متوقع: {e}")
            input("📱 اضغط Enter للمتابعة...")

if __name__ == "__main__":
    main()
