#!/usr/bin/env python3
"""
ملف الإعدادات لمراقب Pastebin
يحتوي على الأنماط والإعدادات القابلة للتخصيص
"""

import re

# إعدادات عامة
DEFAULT_CHECK_INTERVAL = 120  # ثانية
DEFAULT_RESULTS_LIMIT = 50    # عدد اللصق المطلوب فحصها
MAX_MATCHES_DISPLAY = 10      # الحد الأقصى لعرض التطابقات

# الأنماط المطلوب البحث عنها
SEARCH_PATTERNS = {
    # عناوين العملات المشفرة
    'Bitcoin Address': {
        'pattern': re.compile(r'\b[13][a-km-zA-HJ-NP-Z1-9]{26,34}\b'),
        'description': 'عناوين Bitcoin العامة',
        'enabled': True
    },
    
    'Ethereum Address': {
        'pattern': re.compile(r'\b0x[a-fA-F0-9]{40}\b'),
        'description': 'عناوين Ethereum',
        'enabled': True
    },
    
    # أنماط إضافية يمكن تفعيلها
    'Email Address': {
        'pattern': re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'),
        'description': 'عناوين البريد الإلكتروني',
        'enabled': False  # معطل افتراضياً
    },
    
    'IP Address': {
        'pattern': re.compile(r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'),
        'description': 'عناوين IP',
        'enabled': False  # معطل افتراضياً
    },
    
    'Credit Card': {
        'pattern': re.compile(r'\b(?:\d{4}[-\s]?){3}\d{4}\b'),
        'description': 'أرقام بطاقات ائتمان محتملة',
        'enabled': False  # معطل افتراضياً - حساس جداً
    },
    
    'Phone Number': {
        'pattern': re.compile(r'\b(?:\+?1[-.\s]?)?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}\b'),
        'description': 'أرقام هواتف',
        'enabled': False  # معطل افتراضياً
    },
    
    'API Key Pattern': {
        'pattern': re.compile(r'\b[A-Za-z0-9]{32,}\b'),
        'description': 'مفاتيح API محتملة (32+ حرف)',
        'enabled': False  # معطل افتراضياً - قد يعطي نتائج كثيرة
    },
    
    'SSH Private Key': {
        'pattern': re.compile(r'-----BEGIN (?:RSA |OPENSSH |DSA |EC |PGP )?PRIVATE KEY-----'),
        'description': 'مفاتيح SSH خاصة',
        'enabled': False  # معطل افتراضياً - حساس جداً
    },
    
    'AWS Access Key': {
        'pattern': re.compile(r'\bAKIA[0-9A-Z]{16}\b'),
        'description': 'مفاتيح وصول AWS',
        'enabled': False  # معطل افتراضياً - حساس جداً
    }
}

# إعدادات API
API_SETTINGS = {
    'base_url': 'https://pastebin.com',
    'api_url': 'https://pastebin.com/api',
    'user_agent': 'PastebinMonitor/1.0 (Research Purpose)',
    'timeout': 30,
    'retry_attempts': 3,
    'retry_delay': 5  # ثواني
}

# إعدادات العرض
DISPLAY_SETTINGS = {
    'show_paste_content_preview': False,  # عرض معاينة المحتوى
    'preview_length': 100,                # طول المعاينة
    'show_timestamps': True,              # عرض الطوابع الزمنية
    'show_paste_size': True,              # عرض حجم اللصق
    'use_colors': True,                   # استخدام الألوان في العرض
    'max_title_length': 50                # الحد الأقصى لطول العنوان
}

# إعدادات الأمان
SECURITY_SETTINGS = {
    'respect_rate_limits': True,          # احترام حدود المعدل
    'min_request_interval': 2,            # الحد الأدنى بين الطلبات (ثواني)
    'max_content_size': 1024 * 1024,      # الحد الأقصى لحجم المحتوى (1MB)
    'skip_large_pastes': True,            # تخطي اللصق الكبيرة
    'log_activities': False               # تسجيل الأنشطة
}

def get_enabled_patterns():
    """
    الحصول على الأنماط المفعلة فقط
    
    Returns:
        dict: قاموس بالأنماط المفعلة
    """
    enabled_patterns = {}
    for name, config in SEARCH_PATTERNS.items():
        if config['enabled']:
            enabled_patterns[name] = config['pattern']
    return enabled_patterns

def get_pattern_descriptions():
    """
    الحصول على أوصاف الأنماط المفعلة
    
    Returns:
        dict: قاموس بأوصاف الأنماط
    """
    descriptions = {}
    for name, config in SEARCH_PATTERNS.items():
        if config['enabled']:
            descriptions[name] = config['description']
    return descriptions

def enable_pattern(pattern_name):
    """
    تفعيل نمط معين
    
    Args:
        pattern_name: اسم النمط
    """
    if pattern_name in SEARCH_PATTERNS:
        SEARCH_PATTERNS[pattern_name]['enabled'] = True
        print(f"✅ تم تفعيل النمط: {pattern_name}")
    else:
        print(f"❌ النمط غير موجود: {pattern_name}")

def disable_pattern(pattern_name):
    """
    إلغاء تفعيل نمط معين
    
    Args:
        pattern_name: اسم النمط
    """
    if pattern_name in SEARCH_PATTERNS:
        SEARCH_PATTERNS[pattern_name]['enabled'] = False
        print(f"❌ تم إلغاء تفعيل النمط: {pattern_name}")
    else:
        print(f"❌ النمط غير موجود: {pattern_name}")

def list_all_patterns():
    """
    عرض جميع الأنماط المتاحة
    """
    print("📋 الأنماط المتاحة:")
    print("-" * 50)
    for name, config in SEARCH_PATTERNS.items():
        status = "✅ مفعل" if config['enabled'] else "❌ معطل"
        print(f"{status} {name}: {config['description']}")

def validate_config():
    """
    التحقق من صحة الإعدادات
    
    Returns:
        bool: True إذا كانت الإعدادات صحيحة
    """
    errors = []
    
    # التحقق من وجود أنماط مفعلة
    enabled_count = sum(1 for config in SEARCH_PATTERNS.values() if config['enabled'])
    if enabled_count == 0:
        errors.append("لا توجد أنماط مفعلة")
    
    # التحقق من صحة فترة التحقق
    if DEFAULT_CHECK_INTERVAL < 60:
        errors.append("فترة التحقق يجب أن تكون 60 ثانية على الأقل")
    
    # التحقق من صحة حد النتائج
    if DEFAULT_RESULTS_LIMIT > 100:
        errors.append("حد النتائج يجب أن يكون 100 أو أقل")
    
    if errors:
        print("❌ أخطاء في الإعدادات:")
        for error in errors:
            print(f"   - {error}")
        return False
    
    print("✅ الإعدادات صحيحة")
    return True

if __name__ == "__main__":
    # عرض الإعدادات الحالية
    print("⚙️  إعدادات مراقب Pastebin")
    print("=" * 40)
    list_all_patterns()
    print(f"\n⏱️  فترة التحقق الافتراضية: {DEFAULT_CHECK_INTERVAL} ثانية")
    print(f"📊 حد النتائج: {DEFAULT_RESULTS_LIMIT}")
    print(f"🔍 الحد الأقصى لعرض التطابقات: {MAX_MATCHES_DISPLAY}")
    print("\n" + "=" * 40)
    validate_config()
