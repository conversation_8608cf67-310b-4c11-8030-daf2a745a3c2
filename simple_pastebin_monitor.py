#!/usr/bin/env python3
"""
مراقب Pastebin المبسط - للأغراض البحثية والتعليمية
سكربت مبسط لمراقبة أنماط البيانات على Pastebin
"""

import requests
import re
import time
import random
from datetime import datetime

class SimplePastebinMonitor:
    def __init__(self, api_key=None):
        """
        تهيئة مراقب Pastebin المبسط
        
        Args:
            api_key: مفتاح API الخاص بـ Pastebin (اختياري)
        """
        self.api_key = api_key
        self.base_url = "https://pastebin.com"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # الأنماط المطلوب البحث عنها
        self.patterns = {
            'Bitcoin Address': re.compile(r'\b[13][a-km-zA-HJ-NP-Z1-9]{26,34}\b'),
            'Ethereum Address': re.compile(r'\b0x[a-fA-F0-9]{40}\b')
        }
        
        # مجموعة لتتبع اللصق المعالج
        self.processed_pastes = set()
    
    def get_sample_pastes(self):
        """
        إنشاء بيانات تجريبية للاختبار
        في التطبيق الحقيقي، هذه ستكون استدعاءات API حقيقية
        """
        sample_pastes = []
        
        # إنشاء معرفات عشوائية للصق
        for i in range(random.randint(2, 5)):
            paste_id = f"test_{random.randint(1000, 9999)}"
            sample_pastes.append({
                'id': paste_id,
                'url': f"{self.base_url}/{paste_id}",
                'title': f'Sample Paste {i+1}',
                'date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
        
        return sample_pastes
    
    def get_sample_content(self, paste_id):
        """
        إنشاء محتوى تجريبي يحتوي على الأنماط المطلوبة
        """
        sample_contents = [
            """
Configuration backup:
api_key=abc123def456
bitcoin_wallet=**********************************
ethereum_address=******************************************
backup_date=2024-01-15
            """,
            """
Wallet addresses for reference:
BTC: **********************************
ETH: 0x8ba1f109551bD432803012645Hac136c22C501e
BTC: **********************************
ETH: ******************************************
            """,
            """
Random text file with no patterns.
Just some normal content here.
Nothing interesting to find in this paste.
Some random numbers: 12345, 67890
            """,
            """
Multiple cryptocurrency addresses:
Bitcoin addresses:
- **********************************
- **********************************

Ethereum addresses:
- ******************************************
- ******************************************
            """,
            """
System log file:
[2024-01-15 10:30:15] User login successful
[2024-01-15 10:31:22] Transaction initiated
[2024-01-15 10:31:25] BTC address: **********************************
[2024-01-15 10:31:30] ETH address: ******************************************
[2024-01-15 10:32:10] Transaction completed
            """
        ]
        
        # اختيار محتوى عشوائي
        return random.choice(sample_contents)
    
    def search_patterns(self, content):
        """
        البحث عن الأنماط في المحتوى
        
        Args:
            content: محتوى اللصق
            
        Returns:
            قاموس بالأنماط الموجودة
        """
        found_patterns = {}
        
        for pattern_name, pattern_regex in self.patterns.items():
            matches = pattern_regex.findall(content)
            if matches:
                # إزالة التكرارات
                unique_matches = list(set(matches))
                found_patterns[pattern_name] = unique_matches
        
        return found_patterns
    
    def monitor(self, check_interval=30):
        """
        بدء مراقبة Pastebin
        
        Args:
            check_interval: فترة التحقق بالثواني
        """
        print("🔍 بدء مراقبة Pastebin (وضع تجريبي)")
        print(f"⏱️  فترة التحقق: {check_interval} ثانية")
        print("📋 الأنماط المراقبة:")
        for pattern_name in self.patterns.keys():
            print(f"   - {pattern_name}")
        print("-" * 60)
        
        try:
            while True:
                print(f"\n🔄 فحص جديد في {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                
                # الحصول على لصق تجريبي
                sample_pastes = self.get_sample_pastes()
                print(f"📄 تم العثور على {len(sample_pastes)} لصق للفحص")
                
                # فحص كل لصق
                for paste_info in sample_pastes:
                    paste_id = paste_info['id']
                    paste_url = paste_info['url']
                    
                    # تجنب معالجة نفس اللصق مرتين
                    if paste_id in self.processed_pastes:
                        continue
                    
                    self.processed_pastes.add(paste_id)
                    
                    # الحصول على محتوى تجريبي
                    content = self.get_sample_content(paste_id)
                    
                    # البحث عن الأنماط
                    found_patterns = self.search_patterns(content)
                    
                    if found_patterns:
                        print("\n" + "="*60)
                        print(f"🎯 تم العثور على تطابق في: {paste_url}")
                        print(f"📅 التاريخ: {paste_info['date']}")
                        print(f"📝 العنوان: {paste_info['title']}")
                        
                        for pattern_name, matches in found_patterns.items():
                            print(f"\n🔍 النمط: {pattern_name}")
                            print(f"📊 عدد التطابقات: {len(matches)}")
                            for match in matches:
                                print(f"   ✅ {match}")
                        
                        print("="*60)
                    else:
                        print(f"   ℹ️  لا توجد أنماط في {paste_id}")
                
                # انتظار قبل الفحص التالي
                print(f"\n⏳ انتظار {check_interval} ثانية قبل الفحص التالي...")
                time.sleep(check_interval)
                
        except KeyboardInterrupt:
            print("\n🛑 تم إيقاف المراقبة بواسطة المستخدم")
        except Exception as e:
            print(f"\n❌ خطأ غير متوقع: {e}")

def main():
    """
    الدالة الرئيسية
    """
    print("🚀 مراقب Pastebin المبسط للأغراض البحثية")
    print("=" * 50)
    print("ℹ️  هذا إصدار تجريبي يستخدم بيانات وهمية للاختبار")
    print("ℹ️  للاستخدام الحقيقي، تحتاج إلى مفتاح API من Pastebin")
    print("=" * 50)
    
    # إنشاء مثيل المراقب
    monitor = SimplePastebinMonitor()
    
    # بدء المراقبة
    monitor.monitor(check_interval=30)

if __name__ == "__main__":
    main()
